/*
 * @Descripttion: 
 * @version: 
 * @Author: <PERSON><PERSON>
 * @Date: 2023-05-23 15:59:54
 * @LastEditors: baitian.hao
 * @LastEditTime: 2024-05-06 14:57:55
 */

#include <stdio.h>
#include <stdlib.h>
#include <cmath>
#include "utm.h"

double MERCATOR_ORIGIN_E = 275000.02;   // 苏州
double MERCATOR_ORIGIN_N = 3479281.50;
std::string UTM_ZONE = "51R";

double DEG_TO_RAD = PI / 180.0;

double RAD_TO_DEG = 180.0 / PI;

// Grid granularity for rounding UTM coordinates to generate MapXY.
double grid_size = 100000.0;  ///< 100 km grid

// clang-format off
double f = WGS84_F;
double a = WGS84_A;
double n = f/(2.0-f);
double A = a/(1.0+n) * (1.0+ n*n/4.0 + n*n*n*n/64.0);
double alpha1 = 1.0/2.0*n - 2.0/3.0*n*n + 5.0/16.0*n*n*n;
double alpha2 = 13.0/48.0*n*n - 3.0/5.0*n*n*n;
double alpha3 = 61.0/240.0*n*n*n;
double beta1 = 1.0/2.0*n - 2.0/3.0*n*n + 37.0/96.0*n*n*n;
double beta2 = 1.0/48.0*n*n + 1.0/15.0*n*n*n;
double beta3 = 17.0/480.0*n*n*n;
double delta1 = 2.0*n - 2.0/3.0*n*n - 2.0*n*n*n;
double delta2 = 7.0/3.0*n*n - 8.0/5.0*n*n*n;
double delta3 = 56.0/15.0*n*n*n;
double E0 = UTM_FE;
double k0 = UTM_K0;
// clang-format on

char utmLetterDesignator(double Lat)
{
    char LetterDesignator;

    // clang-format off
    if     ((84 >= Lat) && (Lat >= 72))  LetterDesignator = 'X';
    else if ((72 > Lat) && (Lat >= 64))  LetterDesignator = 'W';
    else if ((64 > Lat) && (Lat >= 56))  LetterDesignator = 'V';
    else if ((56 > Lat) && (Lat >= 48))  LetterDesignator = 'U';
    else if ((48 > Lat) && (Lat >= 40))  LetterDesignator = 'T';
    else if ((40 > Lat) && (Lat >= 32))  LetterDesignator = 'S';
    else if ((32 > Lat) && (Lat >= 24))  LetterDesignator = 'R';
    else if ((24 > Lat) && (Lat >= 16))  LetterDesignator = 'Q';
    else if ((16 > Lat) && (Lat >= 8))   LetterDesignator = 'P';
    else if (( 8 > Lat) && (Lat >= 0))   LetterDesignator = 'N';
    else if (( 0 > Lat) && (Lat >= -8))  LetterDesignator = 'M';
    else if ((-8 > Lat) && (Lat >= -16)) LetterDesignator = 'L';
    else if((-16 > Lat) && (Lat >= -24)) LetterDesignator = 'K';
    else if((-24 > Lat) && (Lat >= -32)) LetterDesignator = 'J';
    else if((-32 > Lat) && (Lat >= -40)) LetterDesignator = 'H';
    else if((-40 > Lat) && (Lat >= -48)) LetterDesignator = 'G';
    else if((-48 > Lat) && (Lat >= -56)) LetterDesignator = 'F';
    else if((-56 > Lat) && (Lat >= -64)) LetterDesignator = 'E';
    else if((-64 > Lat) && (Lat >= -72)) LetterDesignator = 'D';
    else if((-72 > Lat) && (Lat >= -80)) LetterDesignator = 'C';
    // 'Z' is an error flag, the Latitude is outside the UTM limits
    else LetterDesignator = 'Z';
    return LetterDesignator;
    // clang-format on
}

void wgs84toUtm(const double lon, const double lat, double* E, double* N, double* k,
                              double* gamma, char* zone)
{
    double N0 = (lat < 0) ? UTM_FN_S:UTM_FN_N;
    double phi = lat;
    double lambda = lon;

    int zone_number;
    zone_number = int((lon * RAD_TO_DEG + 180) / 6) + 1;

    // +3 puts origin in middle of zone
    double lambda0 = ((zone_number - 1) * 6 - 180 + 3) * DEG_TO_RAD;

    // compute the UTM Zone from the latitude and longitude
    if (zone != nullptr) {
        sprintf(zone, "%d%c", zone_number, utmLetterDesignator(lat * RAD_TO_DEG));
    }

    // calc the intermediate value
    // clang-format off
    double t = sinh( atanh(sin(phi)) -  2.0*sqrt(n)/(1.0+n) * atanh( 2.0*sqrt(n)/(1.0+n) * sin(phi) ));
    double xi_quote = atan(t/cos(lambda-lambda0));
    double eta_quote = atanh(sin(lambda-lambda0)/sqrt(1.0+t*t));
    double sigma = 1.0 + 2.0*alpha1*cos(2.0*xi_quote)*cosh(2.0*eta_quote)+
                        4.0*alpha2*cos(4.0*xi_quote)*cosh(4.0*eta_quote)+
                        6.0*alpha3*cos(6.0*xi_quote)*cosh(6.0*eta_quote);
    double tao = 2.0*alpha1*sin(2.0*xi_quote)*sinh(2.0*eta_quote)+
                4.0*alpha2*sin(4.0*xi_quote)*sinh(4.0*eta_quote)+
                6.0*alpha3*sin(6.0*xi_quote)*sinh(6.0*eta_quote);

    if(E != nullptr){
        *E = E0 + k0*A*(eta_quote + alpha1*cos(2.0*xi_quote)*sinh(2.0*eta_quote)
                                + alpha2*cos(4.0*xi_quote)*sinh(4.0*eta_quote)
                                + alpha3*cos(6.0*xi_quote)*sinh(6.0*eta_quote)
                                );
    }

    if(N != nullptr){
        *N = N0 + k0*A*(xi_quote + alpha1*sin(2.0*xi_quote)*cosh(2.0*eta_quote)
                                + alpha2*sin(4.0*xi_quote)*cosh(4.0*eta_quote)
                                + alpha3*sin(6.0*xi_quote)*cosh(6.0*eta_quote)
                                );
    }

    if(k != nullptr){
        *k = k0*A/a*sqrt((1.0 + ((1.0-n)/(1.0+n)*tan(phi))*((1.0-n)/(1.0+n)*tan(phi))) * (sigma*sigma+tao*tao)/(t*t + cos(lambda-lambda0)*cos(lambda-lambda0)));
    }

    if(gamma != nullptr){
        *gamma = atan( (tao*sqrt(1.0+t*t) + sigma*t*tan(lambda-lambda0))/(sigma*sqrt(1.0+t*t) - tao*t*tan(lambda-lambda0)));
    }
    // clang-format on
}

void utmtoWgs84(const double E, const double N, const char* zone, double* lon,
                              double* lat, double* k, double* gamma)
{
    char* zone_letter;
    int zone_number = strtoul(zone, &zone_letter, 10);

    // clang-format off
    double hemi = ((*zone_letter - 'N')) < 0 ? -1.0 : 1.0;
    double N0 = ((*zone_letter - 'N') < 0) ? UTM_FN_S : UTM_FN_N;
    double lambda0 = ((zone_number - 1) * 6 - 180 + 3) * DEG_TO_RAD;

    double xi = (N - N0) / (k0 * A);
    double eta = (E-E0)/(k0*A);

    double xi_quote = xi - beta1*sin(2.0*xi)*cosh(2.0*eta)-
                            beta2*sin(4.0*xi)*cosh(4.0*eta)-
                            beta3*sin(6.0*xi)*cosh(6.0*eta);

    double eta_quote = eta - beta1*cos(2.0*xi)*sinh(2.0*eta)-
                            beta2*cos(4.0*xi)*sinh(4.0*eta)-
                            beta3*cos(6.0*xi)*sinh(6.0*eta);

    double sigma_quote = 1.0 - 2.0*beta1*cos(2.0*xi)*cosh(2.0*eta)-
                                4.0*beta2*cos(4.0*xi)*cosh(4.0*eta)-
                                6.0*beta3*cos(6.0*xi)*cosh(6.0*eta);

    double tao_quote = 2.0*beta1*sin(2.0*xi)*sinh(2.0*eta)+
                        4.0*beta2*sin(4.0*xi)*sinh(4.0*eta)+
                        6.0*beta3*sin(6.0*xi)*sinh(6.0*eta);

    double chi = asin(sin(xi_quote)/cosh(eta_quote));

    double phi = chi + delta1*sin(2.0*chi)+
                        delta2*sin(4.0*chi)+
                        delta3*sin(6.0*chi);

    if(lat != nullptr){
        *lat = phi;
    }

    if(lon != nullptr){
        double lambda = lambda0 + atan(sinh(eta_quote)/cos(xi_quote));
        *lon = lambda;
    }

    if(k != nullptr){
        *k = k0*A/a*sqrt( (1.0 + ((1.0-n)/(1.0+n)*tan(phi))*((1.0-n)/(1.0+n)*tan(phi)) )*((cos(xi_quote)*cos(xi_quote) + sinh(eta_quote)*sinh(eta_quote))/(sigma_quote*sigma_quote + tao_quote*tao_quote)) );
    }

    if(gamma != nullptr){
        *gamma = hemi * atan((tao_quote + sigma_quote*tan(xi_quote)*tanh(eta_quote))/(sigma_quote - tao_quote*tan(xi_quote)*tanh(eta_quote)));
    }
  // clang-format on
}


void utm_to_Wgs84(const double E, const double N, double* lon, double* lat)
{
    double gamma;
    // char   zone[] = UTM_ZONE;
    const char  *zone_tmp = UTM_ZONE.c_str();
    char zone[10];
    std::strcpy(zone, zone_tmp);
    utmtoWgs84(E + MERCATOR_ORIGIN_E, N + MERCATOR_ORIGIN_N, zone, lon, lat, nullptr, &gamma);
    *lon = *lon * 180 / PI;
    *lat = *lat * 180 / PI;
}


/**
 * @description: 
 * @param {double} lon （-180，180）
 * @param {double} lat  (-90, 90)
 * @param {double*} E
 * @param {double*} N
 * @return {*}
 */
void wgs84_to_Utm(const double lon, const double lat, double* E, double* N)
{
    double gamma;
    char   utm_zone_[10];
    double latitude = lat * PI / 180;
    double longitude = lon * PI / 180;
    wgs84toUtm(longitude, latitude, E, N, nullptr, &gamma, utm_zone_);
    *E = *E - MERCATOR_ORIGIN_E;
    *N = *N - MERCATOR_ORIGIN_N;
}

void setUtmOrigin(double x, double y, std::string zone)
{
    MERCATOR_ORIGIN_E = x;
    MERCATOR_ORIGIN_N = y;
    std::string UTM_ZONE = "50N";
}


double dmsToDecimal(double degrees, double minutes, double seconds) {
    double decimalDegrees = degrees + (minutes / 60.0) + (seconds / 3600.0);
    return decimalDegrees;
}