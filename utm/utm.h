/*
 * @Descripttion: 
 * @version: 
 * @Author: <PERSON><PERSON>
 * @Date: 2023-05-23 16:01:01
 * @LastEditors: baitian.hao
 * @LastEditTime: 2024-05-06 15:00:04
 */
#include <string>
#include <cstring>
#ifndef __UTM_H__
#define __UTM_H__

// #define MERCATOR_ORIGIN_E 275000.02    // suzhou
// #define MERCATOR_ORIGIN_N 3479281.50
// #define UTM_ZONE "51R"

// #define MERCATOR_ORIGIN_E 516252.55069669435    // 天津
// #define MERCATOR_ORIGIN_N 4332839.624686965
// #define UTM_ZONE "50N"

#define PI 3.14159265358979323846
// WGS84 Parameters
#define WGS84_A 6378137.0         ///< major axis
#define WGS84_B 6356752.31424518  ///< minor axis
#define WGS84_F 0.003352810664747 /// 0.0033528107      ///< ellipsoid flattening
#define WGS84_E 0.0818191908      ///< first eccentricity
#define WGS84_EP 0.0820944379     ///< second eccentricity

// UTM Parameters
#define UTM_K0 0.9996                    ///< scale factor
#define UTM_FE 500000.0                  ///< false easting
#define UTM_FN_N 0.0                     ///< false northing, northern hemisphere
#define UTM_FN_S 10000000.0              ///< false northing, southern hemisphere
#define UTM_E2 (WGS84_E * WGS84_E)       ///< e^2
#define UTM_E4 (UTM_E2 * UTM_E2)         ///< e^4
#define UTM_E6 (UTM_E4 * UTM_E2)         ///< e^6
#define UTM_EP2 (UTM_E2 / (1 - UTM_E2))  ///< e'^2

char utmLetterDesignator(double Lat);

void wgs84toUtm(const double lon, const double lat, double* E, double* N, double* k,
                              double* gamma, char* zone);

void utmtoWgs84(const double E, const double N, const char* zone, double* lon,
                              double* lat, double* k, double* gamma);

void utm_to_Wgs84(const double E, const double N, double* lon, double* lat);

void wgs84_to_Utm(const double lon, const double lat, double* E, double* N);

void setUtmOrigin(double x, double y, std::string zone);

double dmsToDecimal(double degrees, double minutes, double seconds);

#endif