import json
import re
import os

def parse_cfg(cfg_file):
    config = {}
    stack = [config]
    current_dict = config
    with open(cfg_file, 'r') as file:
        for line in file:
            line = line.strip()
            if not line or line.startswith('#'):
                continue
            if line.endswith('{'):
                section_name = line.split('{')[0].strip()
                new_section = {}
                if section_name in current_dict:
                    if isinstance(current_dict[section_name], list):
                        current_dict[section_name].append(new_section)
                    else:
                        current_dict[section_name] = [current_dict[section_name], new_section]
                else:
                    current_dict[section_name] = new_section
                stack.append(current_dict)
                current_dict = new_section
            elif line.endswith('}'):
                current_dict = stack.pop()
            else:
                key, value = line.split(':', 1)
                key = key.strip()
                value = value.strip()
                # Check for negative numbers
                if re.match(r'^-?\d+$', value):
                    value = int(value)
                elif re.match(r'^-?\d*\.\d+$', value):
                    value = float(value)
                elif re.match(r'^-?\d+(\.\d+)?[eE][-+]?\d+$', value):  # New regex for scientific notation
                    value = float(value)
                elif value.startswith('"') and value.endswith('"'):
                    value = value[1:-1]
                elif value.lower() == 'true':
                    value = True
                elif value.lower() == 'false':
                    value = False
                # Handle multiple radial_distortion entries
                if key == "radial_distortion":
                    if key in current_dict:
                        if isinstance(current_dict[key], list):
                            current_dict[key].append(value)
                        else:
                            current_dict[key] = [current_dict[key], value]
                    else:
                        current_dict[key] = [value]
                else:
                    current_dict[key] = value
    # Convert lidars and cameras to arrays if they are not already
    if 'lidars' in config and not isinstance(config['lidars'], list):
        config['lidars'] = [config['lidars']]
    if 'cameras' in config and not isinstance(config['cameras'], list):
        config['cameras'] = [config['cameras']]
    return config

def cfg_to_json(cfg_file, json_file):
    config = parse_cfg(cfg_file)
    with open(json_file, 'w') as file:
        json.dump(config, file, indent=4)

if __name__ == "__main__":
    cfg_file_path = "./"
    json_file_path = "./"
    # read all cfg files in cfg_file_path
    cfg_files = [f for f in os.listdir(cfg_file_path) if f.endswith('.cfg')]
    for cfg_file in cfg_files:
        json_file = cfg_file.replace('.cfg', '.json')
        cfg_to_json(os.path.join(cfg_file_path, cfg_file), os.path.join(json_file_path, json_file))